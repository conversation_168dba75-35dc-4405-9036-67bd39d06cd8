import React, { useState, useRef, useEffect } from "react";
import { useSelector } from "react-redux";
import {
  Container,
  Header,
  Segment,
  Grid,
  Input,
  Button,
  Icon,
  Divider,
  Message,
  Loader,
} from "semantic-ui-react";
import { convertAddressFull } from "../../app/common/util/util";
import {
  getHelpQuestionsFromDb,
  sendHelpQuestionEmail,
} from "../../app/firestore/firestoreService";

export default function HelpPage() {
  const { transaction } = useSelector((state) => state.transaction);
  const { currentUserProfile } = useSelector((state) => state.profile);

  const [question, setQuestion] = useState("");
  const [userAnswer, setUserAnswer] = useState("");
  const [messages, setMessages] = useState([
    {
      type: "system",
      content:
        "Welcome to TransActioner Help! Ask any question about TransActioner or your current transaction.",
    },
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [helpData, setHelpData] = useState([]);
  const [isLoadingHelpData, setIsLoadingHelpData] = useState(true);

  const messagesEndRef = useRef(null);

  // Load help data from Firestore
  useEffect(() => {
    const fetchHelpData = async () => {
      try {
        setIsLoadingHelpData(true);
        const data = await getHelpQuestionsFromDb();
        setHelpData(data);
      } catch (error) {
        console.error("Error fetching help data:", error);
        setMessages((prev) => [
          ...prev,
          {
            type: "system",
            content:
              "There was an error loading help content. Please try again later.",
          },
        ]);
      } finally {
        setIsLoadingHelpData(false);
      }
    };

    fetchHelpData();
  }, []);

  useEffect(() => {
    return scrollToBottom();
  }, [messages, messagesEndRef]);

  const scrollToBottom = () => {
    return messagesEndRef?.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Find the best matching question from our help data
  const findBestMatch = (userQuestion) => {
    if (!helpData || helpData.length === 0) return null;

    const userQuestionLower = userQuestion.toLowerCase();

    // First try to find an exact match
    const exactMatch = helpData.find(
      (item) =>
        item.question.toLowerCase() === userQuestionLower ||
        item.keywords?.some((keyword) =>
          userQuestionLower.includes(keyword.toLowerCase())
        )
    );

    if (exactMatch) return exactMatch;

    // If no exact match, find the closest match
    let bestMatch = null;
    let highestScore = 0;

    helpData.forEach((item) => {
      const questionWords = item.question.toLowerCase().split(/\s+/);
      const userWords = userQuestionLower.split(/\s+/);

      let matchScore = 0;
      userWords.forEach((word) => {
        if (word.length > 2 && questionWords.includes(word)) {
          matchScore++;
        }
      });

      // Check keywords too
      if (item.keywords) {
        item.keywords.forEach((keyword) => {
          if (userQuestionLower.includes(keyword.toLowerCase())) {
            matchScore += 2; // Keywords are more important
          }
        });
      }

      if (matchScore > highestScore) {
        highestScore = matchScore;
        bestMatch = item;
      }
    });

    // Only return if we have a reasonable match
    return highestScore > 0 ? bestMatch : null;
  };

  // Replace placeholders in the answer with transaction data
  const replaceTransactionPlaceholders = (answer) => {
    if (!transaction) return answer;

    let processedAnswer = answer;

    // Replace transaction placeholders
    const placeholders = {
      "{CLIENT_NAME}": transaction.client
        ? `${transaction.client.firstName || ""} ${
            transaction.client.lastName || ""
          }`.trim()
        : "your client",
      "{TRANSACTION_ADDRESS}": transaction.address
        ? convertAddressFull(transaction.address)
        : "this transaction",
      "{TRANSACTION_STATUS}": transaction.status || "current status",
      "{AGENT_NAME}": currentUserProfile
        ? `${currentUserProfile.firstName || ""} ${
            currentUserProfile.lastName || ""
          }`.trim()
        : "your agent",
    };

    Object.entries(placeholders).forEach(([placeholder, value]) => {
      processedAnswer = processedAnswer.replace(
        new RegExp(placeholder, "g"),
        value
      );
    });

    return processedAnswer;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!question.trim()) return;

    // Add user question to messages
    const userQuestion = question;
    setMessages((prev) => [...prev, { type: "user", content: userQuestion }]);
    setQuestion("");
    setUserAnswer("");
    setIsLoading(true);

    // Simulate a short delay for better UX
    setTimeout(() => {
      let response;

      // Find matching question in our help database
      const match = findBestMatch(userQuestion);

      if (match) {
        // We found a matching question
        response = replaceTransactionPlaceholders(
          "Question: " + match.question + "\n\n" + match.answer
        );
      } else if (transaction?.client?.lastName) {
        // No match but we have transaction context
        response = `I don't have a specific answer for that question. Here's some information about your current transaction: 
        
      Address: ${
        transaction.address
          ? convertAddressFull(transaction.address)
          : "Not specified"
      }
      Status: ${transaction.status || "Not specified"}

      For more specific help, please try rephrasing your question or contact support.`;
      } else {
        // No match and no transaction context
        response =
          "I don't have a specific answer for that question. Please try rephrasing or ask another question about using TransActioner.";
      }
      setUserAnswer(response);

      setMessages((prev) => [
        ...prev,
        { type: "assistant", content: response },
      ]);
      setIsLoading(false);
    }, 1000);

    // Send the user's <NAME_EMAIL>
    try {
      // Only send email if we're in production environment
      sendHelpQuestionEmail(
        userQuestion,
        userAnswer,
        currentUserProfile,
        transaction
      );
    } catch (error) {
      //console.error("Error sending help question email:", error);
      // Don't show error to user, just log it
    }
  };

  return (
    <div className="main-page-wrapper">
      <Container>
        <Grid centered stackable>
          <Grid.Column width={14}>
            <Header as="h1" color="blue">
              <Icon size="small" name="question circle" />
              <Header.Content>
                TransActioner Help
                {transaction?.client && (
                  <Header.Subheader>
                    Current Transaction:
                    {transaction.title?.length > 2 ? transaction.title : ""}
                    {transaction.address?.street
                      ? convertAddressFull(transaction.address)
                      : ""}
                    {transaction.client?.lastName
                      ? " - " +
                        transaction.client?.firstName +
                        " " +
                        transaction.client?.lastName
                      : ""}
                  </Header.Subheader>
                )}
              </Header.Content>
            </Header>
            <span>
              Please feel free to reach out via call or text at 970.412.1020 or
              <NAME_EMAIL> so we can help!
            </span>
            <span>
              <br />
              These are the most frequently asked questions with answers.
            </span>

            <Segment
              style={{
                height: "60vh",
                display: "flex",
                flexDirection: "column",
              }}
            >
              <form onSubmit={handleSubmit}>
                <Input
                  fluid
                  placeholder="Type your question here..."
                  value={question}
                  onChange={(e) => setQuestion(e.target.value)}
                  action={
                    <Button
                      color="blue"
                      icon="send"
                      type="submit"
                      loading={isLoading}
                      disabled={isLoading || !question.trim()}
                    />
                  }
                />
              </form>
              <Divider />
              <div
                style={{
                  flex: 1,
                  overflowY: "auto",
                  padding: "1em",
                  marginBottom: "1em",
                }}
              >
                {isLoadingHelpData && messages.length === 1 ? (
                  <div style={{ textAlign: "center", padding: "2em" }}>
                    <Loader
                      active
                      inline="centered"
                      content="Loading help content..."
                    />
                  </div>
                ) : (
                  messages.map((msg, index) => (
                    <Message
                      key={index}
                      color={
                        msg.type === "user"
                          ? "blue"
                          : msg.type === "system"
                          ? "grey"
                          : null
                      }
                      floating
                      style={{
                        maxWidth: "80%",
                        marginLeft: msg.type === "user" ? "auto" : "0",
                        marginRight: msg.type === "user" ? "0" : "auto",
                        marginBottom: "1em",
                        whiteSpace: "pre-line",
                      }}
                    >
                      {msg.content}
                    </Message>
                  ))
                )}
                <div ref={messagesEndRef} />
              </div>
            </Segment>
          </Grid.Column>
        </Grid>
      </Container>
    </div>
  );
}
