import React, { useState } from "react";
import { useSelector } from "react-redux";
import { Grid, Button, Input } from "semantic-ui-react";
import ManagerUpcomingClosingsList from "./ManagerUpcomingClosingsList";
import { Link } from "../../../../../node_modules/react-router-dom/dist/index";
import { searchFilter } from "../../../../app/common/util/util";

export default function ManagerUpcomingClosingsDashboard() {
  const [searchTerms, setSearchTerms] = useState("");
  const { currentUserProfile } = useSelector((state) => state.profile);

  const {
    transActiveForManager,
    // transUnderContractForManager,
    // transActiveListingForManager,
    // transActiveBuyerForManager,
    transClosedForManager,
  } = useSelector((state) => state.transaction);

  const transactionsToDisplay = searchFilter(
    transActiveForManager,
    searchTerms
  );

  let closedTransactionsByAgent = {};
  transClosedForManager.forEach((transaction) => {
    if (
      transaction.agentProfile?.firstName &&
      transaction.agentProfile?.lastName
    ) {
      let salesPrice = 0;
      if (transaction.salesPrice) {
        salesPrice = parseFloat(transaction.salesPrice.replace(/[$,]/g, ""));
      }
      const agentName = `${transaction.agentProfile?.firstName} ${transaction.agentProfile?.lastName}`;
      if (closedTransactionsByAgent[agentName]) {
        closedTransactionsByAgent[agentName].count += 1;
        closedTransactionsByAgent[agentName].totalSalesPrice += salesPrice;
      } else {
        closedTransactionsByAgent[agentName] = {
          count: 1,
          totalSalesPrice: salesPrice,
        };
      }
    }
  });

  // let numberUnderContract = "0";
  // if (
  //   transUnderContractForManager?.length > 0
  //   // && transActiveForManager?.length > 0
  // ) {
  //   numberUnderContract = (transUnderContractForManager.length
  //   ).toString();
  // }

  return (
    <div className="main-page-wrapper">
      <>
        {currentUserProfile?.managerDetails?.hasAccessToFinancialsDashboard ? (
          <>
            <Grid stackable className="large bottom margin">
              <Grid.Row>
                <Grid.Column
                  computer={16}
                  className="large top margin small bottom margin"
                >
                  <h1
                    className="zero bottom margin"
                    style={{ position: "absolute", bottom: "0" }}
                  >
                    Manager Financials Dashboard
                  </h1>
                </Grid.Column>
              </Grid.Row>
            </Grid>
            <Grid>
              <Grid.Row>
                <Grid.Column computer={5}>
                  <Input
                    type="text"
                    fluid
                    placeholder="Search"
                    value={searchTerms}
                    onChange={(e) => setSearchTerms(e.target.value)}
                  ></Input>
                </Grid.Column>

                <Grid.Column computer={3} tablet={4}>
                  <Button.Group fluid size="small">
                    <Button active as={Link} to="">
                      Upcoming Closings
                    </Button>
                    <Button as={Link} to={`/financialsManagerAgents/`}>
                      Agent List
                    </Button>
                  </Button.Group>
                </Grid.Column>
              </Grid.Row>
            </Grid>

            <Grid>
              <Grid.Column computer={16} className="small bottom margin">
                <h3 className="large bottom margin">
                  Upcoming / Recent Closings
                </h3>
                <ManagerUpcomingClosingsList
                  transactions={transactionsToDisplay}
                />
              </Grid.Column>
            </Grid>
            {/* <Grid>
          <Grid.Column computer={16} className="small bottom margin">
            <h3 className="large bottom margin">Upcoming Closings</h3>
            <Table compact>
              <Table.Header className="mobile hidden">
                <Table.Row className="small-header">
                  <Table.HeaderCell>Agent</Table.HeaderCell>
                  <Table.HeaderCell>Number of Closings</Table.HeaderCell>
                  <Table.HeaderCell>Total Sales Price</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {transUnderContractForManager && Object.entries(transUnderContractForManager).map(
                  ([key, value]) => (
                    <Table.Row key={key}>
                      <Table.Cell>{key}</Table.Cell>
                      <Table.Cell>{value.count}</Table.Cell>
                      <Table.Cell>
                        {value.totalSalesPrice?.toLocaleString("en-US", {
                          style: "currency",
                          currency: "USD",
                          minimumFractionDigits: 0,
                          maximumFractionDigits: 0,
                        })}
                      </Table.Cell>
                    </Table.Row>
                  )
                )}
              </Table.Body>
            </Table>
          </Grid.Column>
        </Grid> */}
          </>
        ) : (
          <>
            <p>
              Contact TransActioner to add on Manager Financials to your
              subscription.
            </p>
            <p>
              <header>What are Manager Financials?</header>
              <ol>
                <li>
                  Store each agents' fees, including splits, donations, desk
                  fees, etc.
                </li>
                <li>
                  Create the Compensation Distribution Authorization (CDA)
                  worksheet to auto-populate the CDA form.
                </li>
                <li>
                  View stats and analysis of fees collected across all
                  transactions each year (coming soon).
                </li>
              </ol>
            </p>
          </>
        )}
      </>
    </div>
  );
}
