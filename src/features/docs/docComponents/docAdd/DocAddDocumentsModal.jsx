import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { Divider, Grid, Header, Icon, Segment } from "semantic-ui-react";
import {
  closeModal,
  openModal,
} from "../../../../app/common/modals/modalSlice";
import ModalWrapper from "../../../../app/common/modals/modalWrapper";
import DocFileUpload from "./DocFileUpload";
import { useMediaQuery } from "react-responsive";
import DocUrlUpload from "./DocUrlUpload";

export default function DocAddDocumentsModal() {
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });
  const { currentUserProfile } = useSelector((state) => state.profile);
  const dispatch = useDispatch();

  function handleSelectForms() {
    dispatch(
      closeModal({
        modalType: "DocAddDocumentsModal",
      })
    );
    dispatch(
      openModal({
        modalType: "DocSelectFormsModal",
      })
    );
  }

  return (
    <>
      <ModalWrapper>
        <Segment placeholder>
          {!currentUserProfile.isBetaTester && (
            <Grid stackable columns={2} textAlign="center">
              {!isMobile && <Divider vertical>Or</Divider>}
              <Grid.Row verticalAlign="middle">
                <Grid.Column>
                  <DocFileUpload status={"In Progress"} />
                </Grid.Column>

                <Grid.Column
                  as={Link}
                  to="#"
                  onClick={() => handleSelectForms()}
                >
                  <Header icon color="grey">
                    <Icon name="file alternate outline" />
                    <br />
                    Select Forms
                  </Header>
                </Grid.Column>
              </Grid.Row>
            </Grid>
          )}

          {currentUserProfile.isBetaTester && (
            <Grid
              stackable
              columns={!currentUserProfile.isBetaTester ? 2 : 5}
              // width={!currentUserProfile.isBetaTester ? 3 : 5}
              textAlign="center"
            >
              <Grid.Row verticalAlign="middle">
                {
                  !currentUserProfile.isBetaTester && !isMobile && (
                    <Divider vertical>Or</Divider>
                  )
                  // only works as 50% as per documentation semantic-ui
                }

                <Grid.Column width={3}>
                  <DocFileUpload status={"In Progress"} />
                </Grid.Column>

                {currentUserProfile.isBetaTester && (
                  <>
                    <Grid.Column width={1}>
                      <Header>OR</Header>
                    </Grid.Column>
                    <Grid.Column width={5}>
                      <>
                        <DocUrlUpload />
                      </>
                    </Grid.Column>
                    <Grid.Column width={1}>
                      <Header>OR</Header>
                    </Grid.Column>
                  </>
                )}

                <Grid.Column
                  width={3}
                  as={Link}
                  to="#"
                  onClick={() => handleSelectForms()}
                >
                  <Header icon color="grey">
                    <Icon name="file alternate outline" />
                    <br />
                    Select Forms
                  </Header>
                </Grid.Column>
              </Grid.Row>
            </Grid>
          )}
        </Segment>
      </ModalWrapper>
    </>
  );
}
