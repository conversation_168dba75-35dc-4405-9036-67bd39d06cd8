import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Segment, Divider } from "semantic-ui-react";
import { useMediaQuery } from "react-responsive";
import { closeModal, openModal } from "../../../../app/common/modals/modalSlice";
import ModalWrapper from "../../../../app/common/modals/modalWrapper";
import {
  updateDocInDb,
  updateDocAddSharingInDb,
  updateTransAddSharingInDb,
  sendDocSharingEmail,
  addHistoryToDb
} from "../../../../app/firestore/firestoreService";
import { partyIsBuyerOrSeller } from "../../../../app/common/util/util";

export default function DocFillableByClientConfirm({
  doc,
  transaction,
  allParties,
  annots,
  signerListDisplay,
  selectedSigner
}) {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentUserProfile } = useSelector((state) => state.profile);
  const [isProcessing, setIsProcessing] = useState(false);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  // Get client parties
  const clientParties = allParties?.filter(party => 
    partyIsBuyerOrSeller(party.role) && party.email
  ) || [];

  async function handleLetClientsFillOut() {
    setIsProcessing(true);
    try {
      // Get agent profile for sharing
      const agentProfile = transaction.agentProfile?.lastName
        ? transaction.agentProfile
        : currentUserProfile;

      // Share document with all client parties
      for (const party of clientParties) {
        try {
          // Add sharing to document
          await updateDocAddSharingInDb(doc.id, party, transaction);

          // Add sharing to transaction
          await updateTransAddSharingInDb(transaction.id, party, transaction);

          // Send sharing email
          sendDocSharingEmail([party], "sharing", agentProfile, transaction);

          // Add history entry
          addHistoryToDb(
            transaction.id,
            currentUserProfile,
            "shared",
            doc.name,
            party
          );
        } catch (shareError) {
          console.error(`Error sharing with ${party.firstName} ${party.lastName}:`, shareError);
          // Continue with other parties even if one fails
        }
      }

      // Close this modal
      dispatch(
        closeModal({
          modalType: "DocFillableByClientConfirm",
        })
      );

      // Navigate back to documents page where clients can now see the shared document
      navigate(`/transactions/${doc.transactionId}/documents`);

      toast.success(`Document shared with ${clientParties.length} client(s). They can now fill it out.`);
    } catch (error) {
      toast.error(error.message);
    } finally {
      setIsProcessing(false);
    }
  }

  async function handleSendForSigningNow() {
    setIsProcessing(true);
    try {
      // Save the annotations first
      await updateDocInDb(doc.id, {
        annotsInProgress: annots,
        signerListInProgress: signerListDisplay,
        selectedSignerInProgress: selectedSigner,
      });

      // Close this modal
      dispatch(
        closeModal({
          modalType: "DocFillableByClientConfirm",
        })
      );

      // Open the SendForSigning modal
      dispatch(
        openModal({
          modalType: "SendForSigning",
        })
      );
    } catch (error) {
      toast.error(error.message);
    } finally {
      setIsProcessing(false);
    }
  }

  function handleCancel() {
    dispatch(
      closeModal({
        modalType: "DocFillableByClientConfirm",
      })
    );
  }

  return (
    <ModalWrapper size="small">
      <Segment>
        <Grid>
          <Grid.Column width={16} className="zero bottom padding">
            <Header as="h2" color="blue" className="zero bottom margin">
              Client Fillable Form
            </Header>
            <Divider className="zero bottom margin" />
          </Grid.Column>
          <Grid.Column width={16}>
            <p className="text-medium">
              This form is designed to be filled out by the client first before they sign.
            </p>
            <p className="text-medium">
              Would you like to share it with your clients so they can fill it out first, 
              then send for signing later?
            </p>
            
            {clientParties.length > 0 && (
              <div style={{ marginTop: "15px", marginBottom: "15px" }}>
                <p className="text-small text-muted">
                  <strong>Your clients:</strong>
                </p>
                <ul style={{ marginLeft: "20px" }}>
                  {clientParties.map(party => (
                    <li key={party.id} className="text-small">
                      {party.firstName} {party.lastName} ({party.role}) - {party.email}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            <Divider className="medium top margin" />
            
            <Button
              primary
              loading={isProcessing}
              disabled={isProcessing}
              type="button"
              floated={isMobile ? null : "right"}
              onClick={() => handleLetClientsFillOut()}
              content="Let Clients Fill Out"
              className={isMobile ? "fluid medium bottom margin" : null}
            />
            
            <Button
              loading={isProcessing}
              disabled={isProcessing}
              type="button"
              floated={isMobile ? null : "right"}
              onClick={() => handleSendForSigningNow()}
              content="Send for Signatures Now"
              className={isMobile ? "fluid medium bottom margin" : null}
            />
            
            <Button
              disabled={isProcessing}
              onClick={() => handleCancel()}
              type="button"
              floated={isMobile ? null : "right"}
              content="Cancel"
              className={isMobile ? "fluid medium" : null}
            />
          </Grid.Column>
        </Grid>
      </Segment>
    </ModalWrapper>
  );
}
